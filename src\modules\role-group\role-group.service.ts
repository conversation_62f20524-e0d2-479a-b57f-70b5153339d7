import { Injectable } from '@nestjs/common';
import { ObjectId } from 'mongodb';

import { MongooseModel } from '~/shared/interfaces/mongoose.interface';
import { ROLE_GROUP_MESSAGES_KEYS } from '~/shared/message-keys/role-group.message-keys';
import { InjectModel } from '~/transformers/model.transformer';
import { buildQuery, QueryParams } from '~/utils';

import { TenantRoleModel } from '../tenant-role/tenant-role.model';
import {
  RoleGroupCreateBodyDto,
  RoleGroupDeleteDto,
  RoleGroupUpdateBodyDto,
} from './dto/role-group.dto';
import { RoleGroupModel } from './role-group.model';

@Injectable()
export class RoleGroupService {
  constructor(
    @InjectModel(RoleGroupModel)
    private readonly roleGroupModel: MongooseModel<RoleGroupModel>,
    @InjectModel(TenantRoleModel)
    private readonly roleModel: MongooseModel<TenantRoleModel>,
  ) {}

  async create(data: RoleGroupCreateBodyDto) {
    await this.validCreateOrUpdateData(null, data);
    return this.roleGroupModel.create(data);
  }

  async findAll(payload: QueryParams) {
    const { query, ...rest } = buildQuery(payload, ['name']);

    let { offset, limit } = rest.options;
    if (payload.pageSize == -1) {
      offset = 0;
      limit = await this.roleGroupModel.countDocuments().lean();
    }
    const aggregate = this.roleGroupModel.aggregate([
      {
        $match: {
          ...query,
          isDeleted: false,
        },
      },
      {
        $lookup: {
          from: this.roleModel.collection.name,
          localField: 'roles',
          foreignField: '_id',
          as: 'roles',
          pipeline: [
            {
              $project: {
                _id: 1,
                key: 1,
                name: 1,
              },
            },
          ],
        },
      },
      {
        $project: {
          _id: 1,
          name: 1,
          description: 1,
          isActive: 1,
          roles: 1,
        },
      },
    ]);

    return this.roleGroupModel.aggregatePaginate(aggregate, {
      ...rest.options,
      offset,
      limit,
    });
  }

  async findOne(id: string) {
    return this.roleGroupModel.findById(id).exec();
  }

  async update(payload: RoleGroupUpdateBodyDto) {
    const { id, ...data } = payload;
    const roleGroup = await this.roleGroupModel
      .findOne({
        _id: new ObjectId(id),
        isDeleted: false,
      })
      .exec();
    if (!roleGroup) {
      throw new Error(ROLE_GROUP_MESSAGES_KEYS.NOT_FOUND);
    }
    await this.validCreateOrUpdateData(id, data);
    data.isActive = data.isActive ?? roleGroup.isActive;
    return this.roleGroupModel
      .findByIdAndUpdate(id, data, {
        returnDocument: 'after',
      })
      .exec();
  }

  async delete(payload: RoleGroupDeleteDto) {
    const { id } = payload;
    const roleGroup = await this.roleGroupModel
      .findOne({
        _id: new ObjectId(id),
        isDeleted: false,
      })
      .exec();
    if (!roleGroup) {
      throw new Error(ROLE_GROUP_MESSAGES_KEYS.NOT_FOUND);
    }
    return this.roleGroupModel
      .findByIdAndUpdate(id, { isDeleted: true }, { returnDocument: 'after' })
      .exec();
  }

  private async validCreateOrUpdateData(id: string | null, data: any) {
    const payloadCheckExistName = {
      name: data.name,
      isDeleted: false,
    };
    if (id) {
      payloadCheckExistName['_id'] = { $ne: new ObjectId(id) };
    }

    const existRoleGroup = await this.roleGroupModel
      .findOne(payloadCheckExistName)
      .exec();

    if (existRoleGroup) {
      throw new Error(ROLE_GROUP_MESSAGES_KEYS.NAME_IS_EXIST);
    }

    if (data.roles && data.roles.length > 0) {
      const roles = await this.roleModel
        .find({ _id: { $in: data.roles } }, { _id: 1 })
        .exec();
      if (roles.length !== data.roles.length) {
        throw new Error(ROLE_GROUP_MESSAGES_KEYS.SOME_ROLE_NOT_EXIST);
      }
    }
  }
}
